import { describe, expect, it } from "vitest";
import type { TableCell, TableProperties } from "@/types/table";
import {
	applyBordersToLogicalState,
	createInitialBorderState,
	getCellBordersFromLogicalState,
} from "../tableUtils";

describe("Logical Border State", () => {
	const createTestCell = (
		colspan = 1,
		rowspan = 1,
		borderSettings?: Partial<{
			top: { width: number; color: string };
			right: { width: number; color: string };
			bottom: { width: number; color: string };
			left: { width: number; color: string };
		}>,
	): TableCell => ({
		content: "Test",
		colspan,
		rowspan,
		backgroundColor: null,
		borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
		borderSettings,
	});

	const createTestTable = (): TableProperties => ({
		rows: 2,
		columns: 2,
		borderWidth: 1,
		borderStyle: "solid",
		borderColor: "#000000",
		cells: [
			[createTestCell(), createTestCell()],
			[createTestCell(), createTestCell()],
		],
	});

	describe("createInitialBorderState", () => {
		it("should create initial border state from table properties", () => {
			const tableProperties = createTestTable();
			const borderState = createInitialBorderState(tableProperties);

			expect(borderState.horizontal).toHaveLength(2);
			expect(borderState.vertical).toHaveLength(2);
			expect(borderState.horizontal[0]).toHaveLength(2);
			expect(borderState.vertical[0]).toHaveLength(2);
		});

		it("should populate borders from existing cell settings", () => {
			const tableProperties = createTestTable();
			tableProperties.cells[0][0].borderSettings = {
				right: { width: 5, color: "#ff0000" },
				bottom: { width: 3, color: "#00ff00" },
			};

			const borderState = createInitialBorderState(tableProperties);

			// Check that the borders were populated
			expect(borderState.vertical[0][0]).toEqual({
				width: 5,
				color: "#ff0000",
				style: "solid",
			});
			expect(borderState.horizontal[0][0]).toEqual({
				width: 3,
				color: "#00ff00",
				style: "solid",
			});
		});
	});

	describe("applyBordersToLogicalState", () => {
		it("should apply borders to the logical state", () => {
			const tableProperties = createTestTable();
			const borderState = createInitialBorderState(tableProperties);

			const selection = {
				start: { row: 0, col: 0 },
				end: { row: 0, col: 0 },
			};

			const activeLines = {
				outerTop: false,
				outerBottom: false,
				outerLeft: false,
				outerRight: true, // Set right border
				innerHorizontal: false,
				innerVertical: false,
			};

			const updatedBorderState = applyBordersToLogicalState(
				borderState,
				selection,
				activeLines,
				5, // 5px width
				"#ff0000", // red color
				tableProperties,
			);

			// Check that the right border was set
			expect(updatedBorderState.vertical[0][0]).toEqual({
				width: 5,
				color: "#ff0000",
				style: "solid",
			});
		});

		it("should handle the user scenario: most recent border wins", () => {
			const tableProperties = createTestTable();
			let borderState = createInitialBorderState(tableProperties);

			// Step 1: Set cell (0,0) right border to 5pt
			const selection1 = {
				start: { row: 0, col: 0 },
				end: { row: 0, col: 0 },
			};
			const activeLines1 = {
				outerTop: false,
				outerBottom: false,
				outerLeft: false,
				outerRight: true,
				innerHorizontal: false,
				innerVertical: false,
			};

			borderState = applyBordersToLogicalState(
				borderState,
				selection1,
				activeLines1,
				5, // 5px
				"#ff0000",
				tableProperties,
			);

			// Step 2: Set cell (0,1) left border to 1pt
			const selection2 = {
				start: { row: 0, col: 1 },
				end: { row: 0, col: 1 },
			};
			const activeLines2 = {
				outerTop: false,
				outerBottom: false,
				outerLeft: true,
				outerRight: false,
				innerHorizontal: false,
				innerVertical: false,
			};

			borderState = applyBordersToLogicalState(
				borderState,
				selection2,
				activeLines2,
				1, // 1px
				"#00ff00",
				tableProperties,
			);

			// The border between (0,0) and (0,1) should now be 1px (most recent)
			expect(borderState.vertical[0][0]).toEqual({
				width: 1,
				color: "#00ff00",
				style: "solid",
			});
		});
	});

	describe("getCellBordersFromLogicalState", () => {
		it("should get borders from logical state", () => {
			const tableProperties = createTestTable();
			const borderState = createInitialBorderState(tableProperties);

			// Set a border in the logical state
			borderState.vertical[0][0] = {
				width: 3,
				color: "#ff0000",
				style: "solid",
			};

			// Update table properties with border state
			tableProperties.borderState = borderState;

			// Get borders for cell (0,0)
			const borders = getCellBordersFromLogicalState(tableProperties, 0, 0);

			// Right border should be the one we set
			expect(borders.right.width).toBe(3);
			expect(borders.right.color).toBe("#ff0000");
			expect(borders.right.style).toBe("solid");
		});

		it("should return 0-width border when logical border is null (hidden border)", () => {
			const tableProperties = createTestTable();
			const borderState = createInitialBorderState(tableProperties);

			// Set a border to null (hidden/removed)
			borderState.vertical[0][0] = null;

			// Update table properties with border state
			tableProperties.borderState = borderState;

			// Get borders for cell (0,0)
			const borders = getCellBordersFromLogicalState(tableProperties, 0, 0);

			// Right border should be hidden (width 0)
			expect(borders.right.width).toBe(0);
		});

		it("should handle all border sides when set to null", () => {
			const tableProperties = createTestTable();
			const borderState = createInitialBorderState(tableProperties);

			// Set all borders around cell (1,1) to null (hidden)
			borderState.horizontal[0][1] = null; // top border
			borderState.horizontal[1][1] = null; // bottom border
			borderState.vertical[1][0] = null; // left border
			borderState.vertical[1][1] = null; // right border

			// Update table properties with border state
			tableProperties.borderState = borderState;

			// Get borders for cell (1,1)
			const borders = getCellBordersFromLogicalState(tableProperties, 1, 1);

			// All borders should be hidden (width 0)
			expect(borders.top.width).toBe(0);
			expect(borders.right.width).toBe(0);
			expect(borders.bottom.width).toBe(0);
			expect(borders.left.width).toBe(0);
		});

		it("should handle UI workflow: setting border width to 0 should hide border", () => {
			const tableProperties = createTestTable();
			let borderState = createInitialBorderState(tableProperties);

			// Simulate UI workflow: user sets border width to 0 to hide it
			const selection = {
				start: { row: 0, col: 0 },
				end: { row: 0, col: 0 },
			};

			const activeLines = {
				outerTop: false,
				outerBottom: false,
				outerLeft: false,
				outerRight: true, // User wants to modify right border
				innerHorizontal: false,
				innerVertical: false,
			};

			// Apply 0 width border (should hide the border)
			borderState = applyBordersToLogicalState(
				borderState,
				selection,
				activeLines,
				0, // 0px width = hidden border
				"#ff0000",
				tableProperties,
			);

			// Update table properties with border state
			tableProperties.borderState = borderState;

			// Get borders for cell (0,0)
			const borders = getCellBordersFromLogicalState(tableProperties, 0, 0);

			// Right border should be hidden (width 0)
			expect(borders.right.width).toBe(0);
		});

		it("should handle topmost and leftmost borders being hidden", () => {
			const tableProperties = createTestTable();
			let borderState = createInitialBorderState(tableProperties);

			// Test topmost border (cell 0,0 top border)
			const topSelection = {
				start: { row: 0, col: 0 },
				end: { row: 0, col: 0 },
			};

			const topActiveLines = {
				outerTop: true, // Hide top border
				outerBottom: false,
				outerLeft: false,
				outerRight: false,
				innerHorizontal: false,
				innerVertical: false,
			};

			// Apply 0 width to top border
			borderState = applyBordersToLogicalState(
				borderState,
				topSelection,
				topActiveLines,
				0, // 0px width = hidden border
				"#ff0000",
				tableProperties,
			);

			// Test leftmost border (cell 0,0 left border)
			const leftSelection = {
				start: { row: 0, col: 0 },
				end: { row: 0, col: 0 },
			};

			const leftActiveLines = {
				outerTop: false,
				outerBottom: false,
				outerLeft: true, // Hide left border
				outerRight: false,
				innerHorizontal: false,
				innerVertical: false,
			};

			// Apply 0 width to left border
			borderState = applyBordersToLogicalState(
				borderState,
				leftSelection,
				leftActiveLines,
				0, // 0px width = hidden border
				"#ff0000",
				tableProperties,
			);

			// Update table properties with border state
			tableProperties.borderState = borderState;

			// Get borders for cell (0,0) - topmost, leftmost cell
			const borders = getCellBordersFromLogicalState(tableProperties, 0, 0);

			// Both top and left borders should be hidden (width 0)
			expect(borders.top.width).toBe(0);
			expect(borders.left.width).toBe(0);
		});

		it("should handle cell (0,1) getting the same border from logical state", () => {
			const tableProperties = createTestTable();
			const borderState = createInitialBorderState(tableProperties);

			// Set a border between cells (0,0) and (0,1)
			borderState.vertical[0][0] = {
				width: 3,
				color: "#ff0000",
				style: "solid",
			};

			tableProperties.borderState = borderState;

			// Both cells should see the same logical border
			const borders00 = getCellBordersFromLogicalState(tableProperties, 0, 0);
			const borders01 = getCellBordersFromLogicalState(tableProperties, 0, 1);

			// Cell (0,0) right border and cell (0,1) left border should be the same
			expect(borders00.right.width).toBe(3);
			expect(borders00.right.color).toBe("#ff0000");
			expect(borders01.left.width).toBe(3);
			expect(borders01.left.color).toBe("#ff0000");
		});
	});
});
